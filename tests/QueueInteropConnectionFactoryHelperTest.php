<?php

declare(strict_types=1);

namespace Imponeer\QueueInteropConnectionFactoryHelper\Tests;

use Error;
use Imponeer\QueueInteropConnectionFactoryHelper\Exceptions\DSNNotSupportedException;
use Imponeer\QueueInteropConnectionFactoryHelper\Exceptions\EmptyDsnException;
use Imponeer\QueueInteropConnectionFactoryHelper\QueueConnectionFactoryHelper;
use Jchook\AssertThrows\AssertThrows;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Throwable;

/**
 * Test QueueInteropConnectionFactoryHelper class
 *
 * @package Imponeer\QueueInteropConnectionFactoryHelper\Tests
 */
final class QueueInteropConnectionFactoryHelperTest extends TestCase
{

	use AssertThrows;

	/**
	 * Test if required static methods exists
	 */
	public function testStaticMethodsExists(): void
	{
		$requiredMethods = ['getFactory', 'createContext'];

		foreach ($requiredMethods as $method) {
			self::assertTrue(
				method_exists(QueueConnectionFactoryHelper::class, $method),
				"Method '{$method}' does not exist in " . QueueConnectionFactoryHelper::class
			);
		}
	}

	public static function provideGoodDSN(): array
	{
		return [
			'amqp' => [
				'dsn' => 'amqp://localhost'
			],
			'stomp' => [
				'dsn' => 'stomp://localhost'
			],
			'gps' => [
				'dsn' => 'gps://localhost'
			],
			'gearman' => [
				'dsn' => 'gearman://localhost'
			],
			'null' => [
				'dsn' => 'null://'
			],
			'file' => [
				'dsn' => 'file://localhost.txt'
			],
			'beanstalk' => [
				'dsn' => 'beanstalk://localhost'
			],
			'sqs' => [
				'dsn' => 'sqs://localhost'
			],
			'kafka' => [
				'dsn' => 'kafka://localhost'
			],
			'redis' => [
				'dsn' => 'redis://localhost'
			],
			'mongodb' => [
				'dsn' => 'mongodb://localhost'
			],
			'mysql' => [
				'dsn' => 'mysql://localhost'
			],
			'wamp' => [
				'dsn' => 'wamp://localhost'
			],
			'ws' => [
				'dsn' => 'ws://localhost'
			],
		];
	}

	/**
	 * Test that all registered DNS'es resolves to something
	 */
	#[DataProvider('provideGoodDSN')]
	public function testIfGoodDNSIsResolvingCorrectly(string $dsn): void
	{
		die(QueueConnectionFactoryHelper::getFactory($dsn));

		$this->assertThrows(
			Throwable::class,
			fn() => QueueConnectionFactoryHelper::getFactory($dsn),
			static function (Throwable $error): void {
				dd($error);
				self::assertMatchesRegularExpression(
					'/Class "([^"]+)" not found|Class \'([^\']+)\' not found/',
					$error->getMessage(),
					'Unexpected error message format'
				);
			}
		);
	}

	/**
	 * Test if unknown DSN throws DSNNotSupportedException
	 */
	public function testIfUnknownDSNIsResolvingCorrectly(): void
	{
		$this->assertThrows(
			DSNNotSupportedException::class,
			fn() => QueueConnectionFactoryHelper::getFactory(sha1((string)microtime(true)) . ':')
		);
	}

	/**
	 * Test if empty DSN throws EmptyDSNException
	 */
	public function testEmptyDSNString(): void
	{
		$this->assertThrows(
			EmptyDsnException::class,
			fn() => QueueConnectionFactoryHelper::getFactory('')
		);

		$this->assertThrows(
			EmptyDsnException::class,
			fn() => QueueConnectionFactoryHelper::getFactory(' ')
		);
	}

}