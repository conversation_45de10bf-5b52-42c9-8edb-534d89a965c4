{"version": 1, "defects": [], "times": {"Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#amqp": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#stomp": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#gps": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#gearman": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#null": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#file": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#beanstalk": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#sqs": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#kafka": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#redis": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#mongodb": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#mysql": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#wamp": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDNSIsResolvingCorrectly#ws": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testEmptyDSNString#spaces": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testEmptyDSNString#empty": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testStaticMethodsExists": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfUnknownDSNIsResolvingCorrectly": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#amqp": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#stomp": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#gps": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#gearman": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#null": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#file": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#beanstalk": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#sqs": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#kafka": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#redis": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#mongodb": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#mysql": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#wamp": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfGoodDsnIsResolvingCorrectly#ws": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testEmptyDsnString#spaces": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testEmptyDsnString#empty": 0, "Imponeer\\QueueInteropConnectionFactoryHelper\\Tests\\QueueInteropConnectionFactoryHelperTest::testIfUnknownDsnIsResolvingCorrectly": 0}}